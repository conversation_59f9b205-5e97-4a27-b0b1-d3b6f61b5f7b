-- Add sample stone data for testing
-- This script adds sample data to stone_type_mast, stone_shape_mast, and stone_size_mast tables

-- Insert sample stone types
INSERT INTO public.stone_type_mast (name, description, is_diamond, is_active) VALUES
('Diamond', 'Natural diamond stones', true, true),
('Ruby', 'Red precious gemstone', false, true),
('Emerald', 'Green precious gemstone', false, true),
('Sapphire', 'Blue precious gemstone', false, true),
('Pearl', 'Natural or cultured pearls', false, true),
('Topaz', 'Semi-precious gemstone in various colors', false, true),
('Amethyst', 'Purple variety of quartz', false, true),
('Garnet', 'Red semi-precious gemstone', false, true),
('Citrine', 'Yellow variety of quartz', false, true),
('Aquamarine', 'Blue-green beryl gemstone', false, true)
ON CONFLICT (name) DO NOTHING;

-- Insert sample stone shapes
INSERT INTO public.stone_shape_mast (name, description, is_active) VALUES
('Round', 'Classic circular cut', true),
('Oval', 'Elongated circular cut', true),
('Pear', 'Teardrop shaped cut', true),
('Marquise', 'Boat-shaped cut with pointed ends', true),
('Princess', 'Square cut with pointed corners', true),
('Emerald', 'Rectangular cut with step facets', true),
('Asscher', 'Square emerald cut', true),
('Cushion', 'Square or rectangular with rounded corners', true),
('Radiant', 'Rectangular with trimmed corners', true),
('Heart', 'Heart-shaped cut', true),
('Baguette', 'Long rectangular cut', true),
('Trillion', 'Triangular cut', true)
ON CONFLICT (name) DO NOTHING;

-- Insert sample stone sizes
INSERT INTO public.stone_size_mast (size_description, min_carat_weight, max_carat_weight, diameter_mm) VALUES
('Melee (0.01-0.18ct)', 0.01, 0.18, 2.5),
('Small (0.19-0.49ct)', 0.19, 0.49, 4.5),
('Medium (0.50-0.99ct)', 0.50, 0.99, 6.5),
('Large (1.00-1.99ct)', 1.00, 1.99, 8.0),
('Extra Large (2.00-2.99ct)', 2.00, 2.99, 9.5),
('Jumbo (3.00ct+)', 3.00, NULL, 11.0),
('Seed Pearl (2-3mm)', NULL, NULL, 2.5),
('Small Pearl (4-6mm)', NULL, NULL, 5.0),
('Medium Pearl (7-9mm)', NULL, NULL, 8.0),
('Large Pearl (10-12mm)', NULL, NULL, 11.0),
('Extra Large Pearl (13mm+)', NULL, NULL, 15.0)
ON CONFLICT (size_description) DO NOTHING;

-- Verify the data was inserted
SELECT 'Stone Types' as table_name, COUNT(*) as count FROM public.stone_type_mast WHERE is_active = true
UNION ALL
SELECT 'Stone Shapes' as table_name, COUNT(*) as count FROM public.stone_shape_mast WHERE is_active = true
UNION ALL
SELECT 'Stone Sizes' as table_name, COUNT(*) as count FROM public.stone_size_mast;

-- Show sample data
SELECT 'STONE TYPES:' as info;
SELECT stone_type_id, name, description, is_diamond, is_active 
FROM public.stone_type_mast 
WHERE is_active = true
ORDER BY name;

SELECT 'STONE SHAPES:' as info;
SELECT shape_id, name, description, is_active 
FROM public.stone_shape_mast 
WHERE is_active = true
ORDER BY name;

SELECT 'STONE SIZES:' as info;
SELECT size_id, size_description, min_carat_weight, max_carat_weight, diameter_mm 
FROM public.stone_size_mast 
ORDER BY COALESCE(min_carat_weight, 0);
