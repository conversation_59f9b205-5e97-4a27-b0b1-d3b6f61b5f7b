-- Fix for Metal Types RLS Issue
-- This script enables Row Level Security on metal_type_mast table
-- and creates the necessary policy for authenticated users

-- Enable RLS on metal_type_mast table (missing from original migration)
ALTER TABLE public.metal_type_mast ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for metal_type_mast (authenticated users only)
CREATE POLICY "Allow authenticated read access to metal types" ON public.metal_type_mast
FOR SELECT TO authenticated USING (true);

-- Also allow authenticated users to manage metal types (for admin operations)
CREATE POLICY "Allow authenticated users to manage metal types" ON public.metal_type_mast
FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Verify the policies were created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'metal_type_mast';
