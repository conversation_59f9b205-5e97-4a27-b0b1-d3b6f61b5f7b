# Database Structure Analysis

Generated on: 2025-01-15 (Based on codebase analysis)

## Executive Summary

This analysis cross-references the actual database schema with the codebase to identify discrepancies and understand the current state of the metal management system.

## Key Findings

### ✅ Tables That Exist (Confirmed from migrations)
- `metal_type_mast` - Modern multi-metal table
- `karat_mast` - Legacy gold-focused purity table  
- `gold_colour_mast` - Legacy gold-focused color table
- `metal_pool` - Metal inventory pool
- `metal_transactions` - Metal movement tracking
- `metal_inventory_transactions` - Location-based metal tracking
- `customer_metal_receipt_items` - Metal receipts from customers

### ❌ Tables Referenced in Code But Missing from Database
- `purity_mast` - Modern multi-metal purity table (referenced in types but doesn't exist)
- `metal_colour_mast` - Modern multi-metal color table (referenced in types but doesn't exist)

### ⚠️ Code-Database Mismatches

#### 1. Type Definitions vs Reality
**File: `src/types/database.ts`**
```typescript
// Note: metal_colour_mast and purity_mast don't exist in current schema
// export type MetalColourMast = Tables['metal_colour_mast']['Row']
// export type PurityMast = Tables['purity_mast']['Row']
```
**Status**: ✅ Code correctly acknowledges these tables don't exist

#### 2. Interface Definitions for Non-Existent Tables
**File: `src/types/masters.ts`**
```typescript
interface MetalColorMaster // References metal_colour_mast (doesn't exist)
interface PurityMaster     // References purity_mast (doesn't exist)
```
**Status**: ❌ Interfaces defined for tables that don't exist

#### 3. UI Navigation References
**File: `src/app/masters/metals/page.tsx`**
```typescript
{
  label: 'Metal Colors',    // Uses legacy gold_colour_mast
  href: '/masters/metal-colors'
},
{
  label: 'Purities',        // Page doesn't exist yet
  href: '/masters/purities'
}
```
**Status**: ⚠️ Mixed - Metal Colors works with legacy table, Purities page missing

## Current Metal System Architecture

### Modern Multi-Metal Foundation
```sql
-- ✅ EXISTS: Modern metal types table
CREATE TABLE public.metal_type_mast (
  metal_type_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name varchar NOT NULL,           -- "Gold", "Silver", "Platinum"
  description text,
  is_active bool DEFAULT true,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);
```

### Legacy Gold-Centric Tables (Still in Use)
```sql
-- ✅ EXISTS: Legacy purity table (gold-focused)
CREATE TABLE public.karat_mast (
  karat_id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  description varchar NOT NULL,    -- "22K", "18K"
  purity numeric NOT NULL,         -- Purity percentage
  standard_wastage numeric NOT NULL,
  density numeric NOT NULL DEFAULT 0,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- ✅ EXISTS: Legacy color table (gold-focused)
CREATE TABLE public.gold_colour_mast (
  gold_colour_id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  description varchar NOT NULL,    -- "Yellow", "Rose", "White"
  processing_complexity_factor numeric NOT NULL,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);
```

### Current Inventory System
```sql
-- ✅ EXISTS: Uses both modern and legacy references
CREATE TABLE public.metal_pool (
  pool_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  metal_type_id uuid REFERENCES public.metal_type_mast(metal_type_id), -- Modern
  karat_id uuid REFERENCES public.karat_mast(karat_id),                -- Legacy
  initial_weight numeric NOT NULL,
  current_weight numeric NOT NULL,
  customer_id uuid REFERENCES public.customer_mast(customer_id),
  uom_id uuid REFERENCES public.uom_mast(uom_id),
  is_active bool DEFAULT true
);
```

## System State Analysis

### What's Working
1. **Metal Types**: Fully functional modern multi-metal system
2. **Legacy Operations**: Gold-focused operations work with existing tables
3. **Inventory Tracking**: Metal pool system operational

### What's Broken/Missing
1. **Modern Purity Management**: No `purity_mast` table for multi-metal purities
2. **Modern Color Management**: No `metal_colour_mast` table for multi-metal colors
3. **UI Consistency**: Purities page doesn't exist, Metal Colors uses legacy table

### What Needs Attention
1. **Mixed Architecture**: System uses both modern (`metal_type_mast`) and legacy (`karat_mast`, `gold_colour_mast`) approaches
2. **Code-Database Sync**: Type definitions exist for non-existent tables
3. **Migration Path**: Need strategy to move from legacy to modern tables

## Recommended Actions

### Phase 1: Create Missing Modern Tables
```sql
-- Create modern purity table
CREATE TABLE public.purity_mast (
  purity_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  metal_type_id uuid REFERENCES public.metal_type_mast(metal_type_id),
  description varchar NOT NULL,
  purity_percentage numeric NOT NULL,
  standard_wastage_percentage numeric NOT NULL,
  density numeric NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- Create modern metal color table  
CREATE TABLE public.metal_colour_mast (
  metal_colour_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  metal_type_id uuid REFERENCES public.metal_type_mast(metal_type_id),
  description varchar NOT NULL,
  processing_complexity_factor numeric NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);
```

### Phase 2: Data Migration
- Migrate `karat_mast` data to `purity_mast` with Gold metal_type_id
- Migrate `gold_colour_mast` data to `metal_colour_mast` with Gold metal_type_id
- Add purities and colors for Silver, Platinum, etc.

### Phase 3: Update System References
- Update `metal_pool` to use `purity_id` instead of `karat_id`
- Update orders and transaction tables
- Create UI pages for modern tables
- Update API endpoints

## Files Requiring Updates

### Database Schema
- Create migration for `purity_mast` and `metal_colour_mast`
- Update foreign key references in dependent tables

### Type Definitions
- `src/types/masters.ts` - Update interfaces to match actual tables
- `src/types/database.ts` - Enable types for new tables

### UI Components
- Create `/masters/purities` page
- Update `/masters/metal-colors` to use modern table
- Update form components and APIs

### API Endpoints
- Create APIs for `purity_mast` and `metal_colour_mast`
- Update existing APIs to handle modern table references

This analysis provides a clear roadmap for modernizing the metal management system while maintaining backward compatibility.
