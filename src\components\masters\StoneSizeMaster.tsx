/**
 * StoneSizeMaster Component
 * Manages the stone size master data for inventory management
 * 
 * @module components/masters
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2 } from 'lucide-react';
import { StoneSizeMast } from '@/types/inventory';
import { stoneSizeMastFields } from '@/components/masters/forms/FormFields';
import { MasterForm } from '@/components/common/Form/MasterForm';
import { FormField } from '@/types/common';
import { useToast } from '@/hooks/useToast';

/**
 * StoneSizeMaster Component
 * Provides CRUD operations for stone size master data
 */
export function StoneSizeMaster() {
  const [stoneSizes, setStoneSizes] = useState<StoneSizeMast[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [selectedItem, setSelectedItem] = useState<StoneSizeMast | null>(null);
  const { showToast } = useToast();

  useEffect(() => {
    fetchStoneSizes();
  }, []);

  /**
   * Fetches stone sizes from the API
   */
  const fetchStoneSizes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/masters/stone-sizes');
      
      if (!response.ok) {
        throw new Error('Failed to fetch stone sizes');
      }
      
      const data = await response.json();
      setStoneSizes(data);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      showToast({
        title: 'Error',
        description: errorMessage || 'Error loading stone sizes',
        type: 'destructive'
      });
      console.error('Error fetching stone sizes:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handles form submission for creating or updating a stone size
   * @param values - The form values
   */
  const handleSubmit = async (values: Partial<StoneSizeMast>) => {
    try {
      const url = '/api/masters/stone-sizes';
      const method = selectedItem ? 'PUT' : 'POST';
      const body = selectedItem
        ? JSON.stringify({ ...values, size_id: selectedItem.size_id })
        : JSON.stringify(values);
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body,
      });
      
      if (!response.ok) {
        throw new Error('Failed to save stone size');
      }
      
      await fetchStoneSizes();
      setShowForm(false);
      setSelectedItem(null);
      showToast({
        title: 'Success',
        description: `Stone size ${selectedItem ? 'updated' : 'created'} successfully`,
        type: 'success'
      });
    } catch (err) {
      showToast({
        title: 'Error',
        description: 'Error saving stone size',
        type: 'destructive'
      });
      console.error('Error saving stone size:', err);
    }
  };

  /**
   * Handles editing a stone size
   * @param item - The stone size to edit
   */
  const handleEdit = (item: StoneSizeMast) => {
    setSelectedItem(item);
    setShowForm(true);
  };

  /**
   * Handles deleting a stone size
   * @param item - The stone size to delete
   */
  const handleDelete = async (item: StoneSizeMast) => {
    if (!confirm(`Are you sure you want to delete ${item.size_description}?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/masters/stone-sizes?id=${item.size_id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete stone size');
      }
      
      await fetchStoneSizes();
      showToast({
        title: 'Success',
        description: 'Stone size deleted successfully',
        type: 'success'
      });
    } catch (err) {
      showToast({
        title: 'Error',
        description: 'Error deleting stone size',
        type: 'destructive'
      });
      console.error('Error deleting stone size:', err);
    }
  };

  /**
   * Handles adding a new stone size
   */
  const handleAdd = () => {
    setSelectedItem(null);
    setShowForm(true);
  };

  /**
   * Handles canceling the form
   */
  const handleCancel = () => {
    setShowForm(false);
    setSelectedItem(null);
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading stone sizes...</div>;
  }

  if (error) {
    return <div className="text-red-500 text-center">Error: {error}</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Stone Sizes</h2>
        <button
          onClick={handleAdd}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Stone Size
        </button>
      </div>

      {showForm && (
        <MasterForm
          config={{
            fields: Object.values(stoneSizeMastFields),
            title: 'Stone Size'
          }}
          initialData={selectedItem}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      )}

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Size Description
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Carat Range
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Diameter (mm)
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {stoneSizes.map((stoneSize) => (
              <tr key={stoneSize.size_id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {stoneSize.size_description}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {stoneSize.min_carat_weight && stoneSize.max_carat_weight
                    ? `${stoneSize.min_carat_weight} - ${stoneSize.max_carat_weight} ct`
                    : stoneSize.min_carat_weight
                    ? `${stoneSize.min_carat_weight}+ ct`
                    : 'N/A'
                  }
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {stoneSize.diameter_mm ? `${stoneSize.diameter_mm} mm` : 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(stoneSize)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Pencil className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(stoneSize)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {stoneSizes.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No stone sizes found. Click "Add Stone Size" to create one.
          </div>
        )}
      </div>
    </div>
  );
}
