/**
 * Authentication fallback utility for API routes
 * Provides service role fallback when RLS blocks unauthenticated requests
 */

import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

/**
 * Creates a Supabase client with authentication fallback
 * First tries with the regular authenticated client, then falls back to service role if needed
 * 
 * @returns Object with supabase client and helper methods
 */
export function createSupabaseWithFallback() {
  const supabase = createRouteHandlerClient({ cookies });

  /**
   * Executes a query with authentication fallback
   * @param queryFn Function that takes a supabase client and returns a query
   * @returns Query result with fallback handling
   */
  async function executeWithFallback<T>(
    queryFn: (client: any) => Promise<{ data: T | null; error: any }>
  ) {
    // Check session for debugging
    const { data: sessionData } = await supabase.auth.getSession();
    
    let data, error;

    // Try with regular client first
    const result = await queryFn(supabase);
    data = result.data;
    error = result.error;

    // If no session or no data returned (likely due to RLS), try with service role as fallback
    if (!sessionData?.session || (data && Array.isArray(data) && data.length === 0)) {
      const serviceSupabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );

      const serviceResult = await queryFn(serviceSupabase);
      data = serviceResult.data;
      error = serviceResult.error;
    }

    return { data, error };
  }

  return {
    supabase,
    executeWithFallback
  };
}
