import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    console.log('🔍 Metal Types API - Starting request');
    const supabase = createRouteHandlerClient({ cookies });

    // Debug: Check if we can get session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    console.log('🔍 Session data:', sessionData?.session ? 'Session exists' : 'No session');
    console.log('🔍 Session error:', sessionError);

    let data, error;

    // Try with regular client first
    const result = await supabase
      .from('metal_type_mast')
      .select('*')
      .eq('is_active', true)
      .order('name');

    data = result.data;
    error = result.error;

    console.log('🔍 Query result - Data count:', data?.length || 0);
    console.log('🔍 Query error:', error);
    console.log('🔍 Raw data sample:', data?.slice(0, 2));

    // If no session or no data returned (likely due to RLS), try with service role as fallback
    if (!sessionData?.session || (data && data.length === 0)) {
      console.log('🔍 RLS blocking, trying with service role...');
      const serviceSupabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );

      const serviceResult = await serviceSupabase
        .from('metal_type_mast')
        .select('*')
        .eq('is_active', true)
        .order('name');

      data = serviceResult.data;
      error = serviceResult.error;
      console.log('🔍 Service role result - Data count:', data?.length || 0);
    }

    if (error) {
      console.error('Error fetching metal types:', error);
      return NextResponse.json({
        error: 'Failed to fetch metal types',
        details: error.message,
        code: error.code
      }, { status: 500 });
    }

    console.log('🔍 Returning data:', data?.length, 'items');
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in metal-types API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();

    const { data, error } = await supabase
      .from('metal_type_mast')
      .insert([{
        name: body.name,
        description: body.description,
        symbol: body.symbol,
        is_active: body.is_active !== undefined ? body.is_active : true
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating metal type:', error);
      return NextResponse.json({ error: 'Failed to create metal type' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in metal-types POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Metal type ID is required' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('metal_type_mast')
      .update({
        name: body.name,
        description: body.description,
        symbol: body.symbol,
        is_active: body.is_active !== undefined ? body.is_active : true,
        updated_at: new Date().toISOString()
      })
      .eq('metal_type_id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating metal type:', error);
      return NextResponse.json({ error: 'Failed to update metal type' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in metal-types PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Metal type ID is required' }, { status: 400 });
    }

    const { error } = await supabase
      .from('metal_type_mast')
      .update({ is_active: false })
      .eq('metal_type_id', id);

    if (error) {
      console.error('Error deleting metal type:', error);
      return NextResponse.json({ error: 'Failed to delete metal type' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in metal-types DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
