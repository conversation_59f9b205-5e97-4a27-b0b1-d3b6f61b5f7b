/**
 * Simple Database Structure Extraction
 * Uses Supabase client to get table information
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function getTableStructure() {
  console.log('🚀 Getting database structure...\n');

  let output = '# Database Structure Analysis\n\n';
  output += `Generated on: ${new Date().toISOString()}\n\n`;

  // Test connection and get basic info
  try {
    // Try to query information_schema directly
    const { data: tables, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_type', 'BASE TABLE');

    if (error) {
      console.log('❌ Cannot access information_schema directly:', error.message);
      
      // Alternative: Try to access known tables to infer structure
      console.log('🔄 Trying alternative approach...');
      
      const knownTables = [
        'metal_type_mast',
        'karat_mast', 
        'gold_colour_mast',
        'purity_mast',
        'metal_colour_mast',
        'metal_pool',
        'orders',
        'customer_mast',
        'worker_mast',
        'process_mast',
        'stone_type_mast',
        'stone_shape_mast',
        'stone_size_mast'
      ];

      output += '## Table Existence Check\n\n';
      output += '| Table Name | Status | Error |\n';
      output += '|------------|--------|-------|\n';

      for (const tableName of knownTables) {
        try {
          const { data, error: tableError } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);

          if (tableError) {
            const status = tableError.message.includes('does not exist') ? '❌ MISSING' : '⚠️ ACCESS DENIED';
            output += `| ${tableName} | ${status} | ${tableError.message} |\n`;
          } else {
            output += `| ${tableName} | ✅ EXISTS | - |\n`;
            
            // If we can access the table, try to get column info
            if (data !== null) {
              const sampleRecord = Array.isArray(data) && data.length > 0 ? data[0] : {};
              const columns = Object.keys(sampleRecord);
              
              if (columns.length > 0) {
                output += `\n### Columns in \`${tableName}\`:\n`;
                columns.forEach(col => {
                  output += `- ${col}\n`;
                });
                output += '\n';
              }
            }
          }
        } catch (err) {
          output += `| ${tableName} | ❌ ERROR | ${err} |\n`;
        }
      }

    } else {
      console.log('✅ Successfully accessed information_schema');
      output += '## All Tables\n\n';
      tables?.forEach(table => {
        output += `- ${table.table_name}\n`;
      });
      output += '\n';
    }

    // Check specific metal-related tables with detailed analysis
    output += '\n## Metal System Analysis\n\n';
    
    const metalTables = {
      'metal_type_mast': 'Modern multi-metal types table',
      'purity_mast': 'Modern multi-metal purity table (expected)',
      'metal_colour_mast': 'Modern multi-metal color table (expected)', 
      'karat_mast': 'Legacy gold-focused purity table',
      'gold_colour_mast': 'Legacy gold-focused color table'
    };

    for (const [tableName, description] of Object.entries(metalTables)) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(5);

        if (error) {
          output += `### ❌ \`${tableName}\` - ${description}\n`;
          output += `**Status**: MISSING or INACCESSIBLE\n`;
          output += `**Error**: ${error.message}\n\n`;
        } else {
          output += `### ✅ \`${tableName}\` - ${description}\n`;
          output += `**Status**: EXISTS and ACCESSIBLE\n`;
          output += `**Record Count**: ${Array.isArray(data) ? data.length : 0} (sample)\n`;
          
          if (Array.isArray(data) && data.length > 0) {
            const columns = Object.keys(data[0]);
            output += `**Columns**: ${columns.join(', ')}\n`;
            
            // Show sample data
            output += `**Sample Data**:\n`;
            output += '```json\n';
            output += JSON.stringify(data[0], null, 2);
            output += '\n```\n';
          }
          output += '\n';
        }
      } catch (err) {
        output += `### ❌ \`${tableName}\` - ${description}\n`;
        output += `**Status**: ERROR\n`;
        output += `**Error**: ${err}\n\n`;
      }
    }

  } catch (err) {
    console.error('❌ General error:', err);
    output += `## Error\n\n${err}\n\n`;
  }

  // Save results
  const outputPath = path.join(process.cwd(), 'database-structure-simple.md');
  fs.writeFileSync(outputPath, output);
  
  console.log(`✅ Analysis complete! Results saved to: ${outputPath}`);
}

getTableStructure().catch(console.error);
