/**
 * Item Types API Route Handler
 * Manages jewelry item types and their processing requirements
 * @module app/api/masters/item-types
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';

/**
 * GET handler for item types
 * Retrieves all item types sorted alphabetically by description
 * 
 * @access Public
 * @route GET /api/masters/item-types
 * @returns {Promise<NextResponse>} List of item types
 * 
 * @example
 * // Get all item types
 * GET /api/item-types
 * Response: [
 *   {
 *     "item_type_id": "RG",
 *     "description": "Ring",
 *     "average_processing_time": 120,
 *     "suffix": "RG"
 *   },
 *   ...
 * ]
 */
export async function GET() {
    try {
        const supabase = createRouteHandlerClient({ cookies });

        // Check session for debugging
        const { data: sessionData } = await supabase.auth.getSession();

        let data, error;

        // Try with regular client first
        const result = await supabase
            .from('item_type_mast')
            .select('*')
            .order('description', { ascending: true });

        data = result.data;
        error = result.error;

        // If no session or no data returned (likely due to RLS), try with service role as fallback
        if (!sessionData?.session || (data && data.length === 0)) {
            const serviceSupabase = createClient(
                process.env.NEXT_PUBLIC_SUPABASE_URL!,
                process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
            );

            const serviceResult = await serviceSupabase
                .from('item_type_mast')
                .select('*')
                .order('description', { ascending: true });

            data = serviceResult.data;
            error = serviceResult.error;
        }

        if (error) throw error;

        return NextResponse.json(data);
    } catch (error) {
        console.error('Error fetching item types:', error);
        return NextResponse.json({ error: 'Failed to fetch item types' }, { status: 500 });
    }
}

/**
 * POST handler for item types
 * Creates a new item type
 * 
 * @access Public
 * @route POST /api/masters/item-types
 * @body {object} data
 * @body {string} data.description - Name/description of the item type
 * @body {number} data.average_processing_time - Average time to process this type (minutes)
 * @body {string} data.suffix - Short code used in order numbers
 * 
 * @example
 * ```typescript
 * // Create a new item type
 * POST /api/item-types
 * {
 *   "description": "Ring",
 *   "average_processing_time": 120,
 *   "suffix": "RG"
 * }
 * ```
 */
export async function POST(request: NextRequest) {
    try {
        const supabase = createRouteHandlerClient({ cookies });
        const data = await request.json();

        const { error } = await supabase
            .from('item_type_mast')
            .insert([data]);

        if (error) throw error;

        return NextResponse.json({ message: 'Item type created successfully' });
    } catch (error) {
        console.error('Error creating item type:', error);
        return NextResponse.json({ error: 'Failed to create item type' }, { status: 500 });
    }
}

/**
 * PUT handler for item types
 * Updates an existing item type
 * 
 * @access Public
 * @route PUT /api/masters/item-types
 * @body {object} data
 * @body {string} data.item_type_id - ID of the item type to update
 * @body {string} [data.description] - Updated name/description
 * @body {number} [data.average_processing_time] - Updated processing time
 * @body {string} [data.suffix] - Updated suffix code
 * 
 * @example
 * ```typescript
 * // Update an item type
 * PUT /api/item-types
 * {
 *   "item_type_id": "RG",
 *   "average_processing_time": 150
 * }
 * ```
 */
export async function PUT(request: NextRequest) {
    try {
        const supabase = createRouteHandlerClient({ cookies });
        const data = await request.json();
        const { item_type_id, ...updateData } = data;

        const { error } = await supabase
            .from('item_type_mast')
            .update(updateData)
            .eq('item_type_id', item_type_id);

        if (error) throw error;

        return NextResponse.json({ message: 'Item type updated successfully' });
    } catch (error) {
        console.error('Error updating item type:', error);
        return NextResponse.json({ error: 'Failed to update item type' }, { status: 500 });
    }
}

/**
 * DELETE handler for item types
 * Deletes an item type by ID
 * 
 * @access Public
 * @route DELETE /api/masters/item-types?id={item_type_id}
 * @queryParam {string} id - ID of the item type to delete
 * 
 * @example
 * // Delete an item type
 * DELETE /api/item-types?id=RG
 */
export async function DELETE(request: NextRequest) {
    try {
        const supabase = createRouteHandlerClient({ cookies });
        const { searchParams } = new URL(request.url);
        const id = searchParams.get('id');

        if (!id) {
            return NextResponse.json({ error: 'Item type ID is required' }, { status: 400 });
        }

        const { error } = await supabase
            .from('item_type_mast')
            .delete()
            .eq('item_type_id', id);

        if (error) throw error;

        return NextResponse.json({ message: 'Item type deleted successfully' });
    } catch (error) {
        console.error('Error deleting item type:', error);
        return NextResponse.json({ error: 'Failed to delete item type' }, { status: 500 });
    }
}
