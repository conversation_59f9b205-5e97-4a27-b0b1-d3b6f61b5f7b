/**
 * Database Structure Analysis Script
 * Connects to Supabase and extracts complete database structure
 * Saves results to database-structure-analysis.md for reference
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runQuery(sql: string, description: string) {
  console.log(`🔍 ${description}...`);
  try {
    const { data, error } = await supabase.from('').select().limit(0); // Test connection first
    if (error && error.message.includes('relation') === false) {
      console.error('Connection error:', error);
      return null;
    }

    // Use raw SQL query
    const { data: result, error: queryError } = await supabase.rpc('exec_sql', { query: sql });
    if (queryError) {
      console.error(`❌ Error in ${description}:`, queryError);
      return null;
    }
    return result;
  } catch (err) {
    console.error(`❌ Exception in ${description}:`, err);
    return null;
  }
}

async function analyzeDatabase() {
  console.log('🚀 Starting database structure analysis...\n');

  let output = '# Database Structure Analysis\n\n';
  output += `Generated on: ${new Date().toISOString()}\n\n`;

  // 1. Get all tables summary
  const tablesSql = `
    SELECT 
        table_name,
        (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = 'public') as column_count
    FROM information_schema.tables t
    WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
    ORDER BY table_name;
  `;

  try {
    const { data: tables, error } = await supabase.rpc('exec_sql', { query: tablesSql });
    if (error) {
      console.error('Error getting tables:', error);
    } else if (tables) {
      output += '## All Tables Summary\n\n';
      output += '| Table Name | Column Count |\n';
      output += '|------------|-------------|\n';
      tables.forEach((table: any) => {
        output += `| ${table.table_name} | ${table.column_count} |\n`;
      });
      output += '\n';
    }
  } catch (err) {
    console.error('Exception getting tables:', err);
  }

  // 2. Check specific metal-related tables
  const metalTablesSql = `
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        AND (table_name LIKE '%metal%' 
             OR table_name LIKE '%karat%' 
             OR table_name LIKE '%gold%' 
             OR table_name LIKE '%purity%'
             OR table_name LIKE '%colour%'
             OR table_name LIKE '%color%')
    ORDER BY table_name;
  `;

  try {
    const { data: metalTables, error } = await supabase.rpc('exec_sql', { query: metalTablesSql });
    if (error) {
      console.error('Error getting metal tables:', error);
    } else if (metalTables) {
      output += '## Metal-Related Tables\n\n';
      metalTables.forEach((table: any) => {
        output += `- ${table.table_name}\n`;
      });
      output += '\n';
    }
  } catch (err) {
    console.error('Exception getting metal tables:', err);
  }

  // 3. Check for specific tables mentioned in code
  const checkTablesSql = `
    SELECT 
        'metal_type_mast' as table_name,
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'metal_type_mast' AND table_schema = 'public') 
            THEN 'EXISTS'
            ELSE 'MISSING'
        END as status
    UNION ALL
    SELECT 
        'purity_mast',
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'purity_mast' AND table_schema = 'public') 
            THEN 'EXISTS'
            ELSE 'MISSING'
        END
    UNION ALL
    SELECT 
        'metal_colour_mast',
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'metal_colour_mast' AND table_schema = 'public') 
            THEN 'EXISTS'
            ELSE 'MISSING'
        END
    UNION ALL
    SELECT 
        'karat_mast',
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'karat_mast' AND table_schema = 'public') 
            THEN 'EXISTS (legacy)'
            ELSE 'MISSING'
        END
    UNION ALL
    SELECT 
        'gold_colour_mast',
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'gold_colour_mast' AND table_schema = 'public') 
            THEN 'EXISTS (legacy)'
            ELSE 'MISSING'
        END;
  `;

  try {
    const { data: tableCheck, error } = await supabase.rpc('exec_sql', { query: checkTablesSql });
    if (error) {
      console.error('Error checking tables:', error);
    } else if (tableCheck) {
      output += '## Table Existence Check\n\n';
      output += '| Table Name | Status |\n';
      output += '|------------|--------|\n';
      tableCheck.forEach((table: any) => {
        const icon = table.status.includes('EXISTS') ? '✅' : '❌';
        output += `| ${table.table_name} | ${icon} ${table.status} |\n`;
      });
      output += '\n';
    }
  } catch (err) {
    console.error('Exception checking tables:', err);
  }

  // Save to file
  const outputPath = path.join(process.cwd(), 'database-structure-analysis.md');
  fs.writeFileSync(outputPath, output);
  
  console.log(`✅ Database structure analysis complete!`);
  console.log(`📄 Results saved to: ${outputPath}`);
}

// Run the analysis
analyzeDatabase().catch(console.error);
