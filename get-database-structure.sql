-- Complete Database Structure Analysis Script
-- This script extracts the complete structure of all tables, functions, policies, etc.
-- Run this in Supabase SQL Editor to get the exact current state

-- =====================================================
-- 1. LIST ALL TABLES (SUMMARY)
-- =====================================================
SELECT
    '=== ALL TABLES SUMMARY ===' as info;

SELECT
    table_name,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = 'public') as column_count
FROM information_schema.tables t
WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE'
ORDER BY table_name;

SELECT 
    t.table_name,
    c.column_name,
    c.data_type,
    c.character_maximum_length,
    c.is_nullable,
    c.column_default,
    CASE 
        WHEN pk.column_name IS NOT NULL THEN 'PRIMARY KEY'
        WHEN fk.column_name IS NOT NULL THEN 'FOREIGN KEY -> ' || fk.foreign_table_name || '(' || fk.foreign_column_name || ')'
        ELSE ''
    END as constraints
FROM information_schema.tables t
LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
LEFT JOIN (
    SELECT 
        kcu.column_name,
        kcu.table_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
    WHERE tc.constraint_type = 'PRIMARY KEY'
        AND tc.table_schema = 'public'
) pk ON c.table_name = pk.table_name AND c.column_name = pk.column_name
LEFT JOIN (
    SELECT 
        kcu.column_name,
        kcu.table_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage ccu 
        ON ccu.constraint_name = tc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
) fk ON c.table_name = fk.table_name AND c.column_name = fk.column_name
WHERE t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    AND c.table_schema = 'public'
ORDER BY t.table_name, c.ordinal_position;

-- =====================================================
-- 2. LIST ALL TABLES (SUMMARY)
-- =====================================================
SELECT 
    '=== ALL TABLES SUMMARY ===' as info;

SELECT 
    table_name,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = 'public') as column_count
FROM information_schema.tables t
WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- =====================================================
-- 3. METAL-RELATED TABLES SPECIFICALLY
-- =====================================================
SELECT 
    '=== METAL-RELATED TABLES ===' as info;

SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
    AND (table_name LIKE '%metal%' 
         OR table_name LIKE '%karat%' 
         OR table_name LIKE '%gold%' 
         OR table_name LIKE '%purity%'
         OR table_name LIKE '%colour%'
         OR table_name LIKE '%color%')
ORDER BY table_name;

-- =====================================================
-- 4. CHECK FOR SPECIFIC TABLES MENTIONED IN CODE
-- =====================================================
SELECT 
    '=== CHECKING SPECIFIC TABLES ===' as info;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'metal_type_mast' AND table_schema = 'public') 
        THEN '✅ metal_type_mast EXISTS'
        ELSE '❌ metal_type_mast MISSING'
    END as metal_type_status
UNION ALL
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'purity_mast' AND table_schema = 'public') 
        THEN '✅ purity_mast EXISTS'
        ELSE '❌ purity_mast MISSING'
    END
UNION ALL
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'metal_colour_mast' AND table_schema = 'public') 
        THEN '✅ metal_colour_mast EXISTS'
        ELSE '❌ metal_colour_mast MISSING'
    END
UNION ALL
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'karat_mast' AND table_schema = 'public') 
        THEN '✅ karat_mast EXISTS (legacy)'
        ELSE '❌ karat_mast MISSING'
    END
UNION ALL
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'gold_colour_mast' AND table_schema = 'public') 
        THEN '✅ gold_colour_mast EXISTS (legacy)'
        ELSE '❌ gold_colour_mast MISSING'
    END;

-- =====================================================
-- 5. ALL FUNCTIONS AND PROCEDURES
-- =====================================================
SELECT 
    '=== FUNCTIONS AND PROCEDURES ===' as info;

SELECT 
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines
WHERE routine_schema = 'public'
ORDER BY routine_name;

-- =====================================================
-- 6. ROW LEVEL SECURITY POLICIES
-- =====================================================
SELECT 
    '=== RLS POLICIES ===' as info;

SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- =====================================================
-- 7. INDEXES
-- =====================================================
SELECT 
    '=== INDEXES ===' as info;

SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
ORDER BY tablename, indexname;
