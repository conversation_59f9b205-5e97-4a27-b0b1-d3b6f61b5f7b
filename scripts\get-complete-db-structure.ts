/**
 * Complete Database Structure Extraction Script
 * Connects directly to Supabase PostgreSQL and extracts complete database structure
 * including tables, columns, functions, policies, indexes, etc.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function executeQuery(sql: string): Promise<any[] | null> {
  try {
    // Try using the SQL query directly through Supabase client
    const { data, error } = await supabase.rpc('exec_sql', { query: sql });
    if (error) {
      console.error('Query error:', error);
      // Try alternative approach
      const { data: altData, error: altError } = await supabase.rpc('sql', { query: sql });
      if (altError) {
        console.error('Alternative query error:', altError);
        return null;
      }
      return altData;
    }
    return data;
  } catch (err) {
    console.error('Exception:', err);
    return null;
  }
}

async function getCompleteStructure() {
  console.log('🚀 Starting complete database structure extraction...\n');

  let output = '# Complete Database Structure Analysis\n\n';
  output += `Generated on: ${new Date().toISOString()}\n`;
  output += `Database: ${supabaseUrl}\n\n`;

  // 1. Get all tables with column counts
  console.log('📊 Getting all tables...');
  const tablesQuery = `
    SELECT 
        t.table_name,
        t.table_type,
        (SELECT COUNT(*) FROM information_schema.columns c WHERE c.table_name = t.table_name AND c.table_schema = 'public') as column_count,
        obj_description(c.oid) as table_comment
    FROM information_schema.tables t
    LEFT JOIN pg_class c ON c.relname = t.table_name
    WHERE t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
    ORDER BY t.table_name;
  `;

  const tables = await executeQuery(tablesQuery);
  if (tables) {
    output += '## All Tables Summary\n\n';
    output += '| Table Name | Columns | Comment |\n';
    output += '|------------|---------|----------|\n';
    tables.forEach((table: any) => {
      output += `| ${table.table_name} | ${table.column_count} | ${table.table_comment || '-'} |\n`;
    });
    output += '\n';
  }

  // 2. Get detailed structure for all tables
  console.log('🔍 Getting detailed table structures...');
  if (tables) {
    for (const table of tables) {
      const structureQuery = `
        SELECT 
            c.column_name,
            c.data_type,
            c.character_maximum_length,
            c.numeric_precision,
            c.numeric_scale,
            c.is_nullable,
            c.column_default,
            col_description(pgc.oid, c.ordinal_position) as column_comment,
            CASE 
                WHEN pk.column_name IS NOT NULL THEN 'PRIMARY KEY'
                WHEN fk.column_name IS NOT NULL THEN 'FK → ' || fk.foreign_table_name || '(' || fk.foreign_column_name || ')'
                WHEN uq.column_name IS NOT NULL THEN 'UNIQUE'
                ELSE ''
            END as constraints
        FROM information_schema.columns c
        LEFT JOIN pg_class pgc ON pgc.relname = c.table_name
        LEFT JOIN (
            SELECT 
                kcu.column_name,
                kcu.table_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            WHERE tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_schema = 'public'
        ) pk ON c.table_name = pk.table_name AND c.column_name = pk.column_name
        LEFT JOIN (
            SELECT 
                kcu.column_name,
                kcu.table_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage ccu 
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = 'public'
        ) fk ON c.table_name = fk.table_name AND c.column_name = fk.column_name
        LEFT JOIN (
            SELECT 
                kcu.column_name,
                kcu.table_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            WHERE tc.constraint_type = 'UNIQUE'
                AND tc.table_schema = 'public'
        ) uq ON c.table_name = uq.table_name AND c.column_name = uq.column_name
        WHERE c.table_name = '${table.table_name}'
            AND c.table_schema = 'public'
        ORDER BY c.ordinal_position;
      `;

      const structure = await executeQuery(structureQuery);
      if (structure && structure.length > 0) {
        output += `## Table: \`${table.table_name}\`\n\n`;
        output += '| Column | Type | Length | Nullable | Default | Constraints | Comment |\n';
        output += '|--------|------|--------|----------|---------|-------------|----------|\n';
        structure.forEach((col: any) => {
          const typeInfo = col.character_maximum_length 
            ? `${col.data_type}(${col.character_maximum_length})`
            : col.numeric_precision 
            ? `${col.data_type}(${col.numeric_precision},${col.numeric_scale || 0})`
            : col.data_type;
          
          output += `| ${col.column_name} | ${typeInfo} | ${col.character_maximum_length || '-'} | ${col.is_nullable} | ${col.column_default || '-'} | ${col.constraints || '-'} | ${col.column_comment || '-'} |\n`;
        });
        output += '\n';
      }
    }
  }

  // 3. Get all functions and procedures
  console.log('⚙️ Getting functions and procedures...');
  const functionsQuery = `
    SELECT 
        p.proname as function_name,
        pg_get_function_result(p.oid) as return_type,
        pg_get_function_arguments(p.oid) as arguments,
        CASE p.prokind
            WHEN 'f' THEN 'FUNCTION'
            WHEN 'p' THEN 'PROCEDURE'
            WHEN 'a' THEN 'AGGREGATE'
            WHEN 'w' THEN 'WINDOW'
            ELSE 'OTHER'
        END as function_type,
        l.lanname as language,
        obj_description(p.oid) as description
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    JOIN pg_language l ON p.prolang = l.oid
    WHERE n.nspname = 'public'
        AND p.proname NOT LIKE 'pg_%'
    ORDER BY p.proname;
  `;

  const functions = await executeQuery(functionsQuery);
  if (functions && functions.length > 0) {
    output += '## Functions and Procedures\n\n';
    output += '| Name | Type | Arguments | Returns | Language | Description |\n';
    output += '|------|------|-----------|---------|----------|-------------|\n';
    functions.forEach((func: any) => {
      output += `| ${func.function_name} | ${func.function_type} | ${func.arguments || '-'} | ${func.return_type || '-'} | ${func.language} | ${func.description || '-'} |\n`;
    });
    output += '\n';
  }

  // 4. Get RLS policies
  console.log('🔒 Getting RLS policies...');
  const policiesQuery = `
    SELECT 
        schemaname,
        tablename,
        policyname,
        permissive,
        roles,
        cmd,
        qual,
        with_check
    FROM pg_policies
    WHERE schemaname = 'public'
    ORDER BY tablename, policyname;
  `;

  const policies = await executeQuery(policiesQuery);
  if (policies && policies.length > 0) {
    output += '## Row Level Security Policies\n\n';
    output += '| Table | Policy Name | Command | Roles | Permissive | Condition |\n';
    output += '|-------|-------------|---------|-------|------------|----------|\n';
    policies.forEach((policy: any) => {
      output += `| ${policy.tablename} | ${policy.policyname} | ${policy.cmd || 'ALL'} | ${policy.roles ? policy.roles.join(', ') : 'ALL'} | ${policy.permissive} | ${policy.qual || '-'} |\n`;
    });
    output += '\n';
  }

  // 5. Get indexes
  console.log('📇 Getting indexes...');
  const indexesQuery = `
    SELECT 
        schemaname,
        tablename,
        indexname,
        indexdef
    FROM pg_indexes
    WHERE schemaname = 'public'
        AND indexname NOT LIKE '%_pkey'
    ORDER BY tablename, indexname;
  `;

  const indexes = await executeQuery(indexesQuery);
  if (indexes && indexes.length > 0) {
    output += '## Indexes\n\n';
    output += '| Table | Index Name | Definition |\n';
    output += '|-------|------------|------------|\n';
    indexes.forEach((index: any) => {
      output += `| ${index.tablename} | ${index.indexname} | \`${index.indexdef}\` |\n`;
    });
    output += '\n';
  }

  // 6. Check for specific metal-related tables
  console.log('🔍 Checking metal-related tables...');
  const metalCheckQuery = `
    SELECT 
        'metal_type_mast' as table_name,
        CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'metal_type_mast' AND table_schema = 'public') THEN 'EXISTS' ELSE 'MISSING' END as status
    UNION ALL SELECT 'purity_mast', CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'purity_mast' AND table_schema = 'public') THEN 'EXISTS' ELSE 'MISSING' END
    UNION ALL SELECT 'metal_colour_mast', CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'metal_colour_mast' AND table_schema = 'public') THEN 'EXISTS' ELSE 'MISSING' END
    UNION ALL SELECT 'karat_mast', CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'karat_mast' AND table_schema = 'public') THEN 'EXISTS (legacy)' ELSE 'MISSING' END
    UNION ALL SELECT 'gold_colour_mast', CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'gold_colour_mast' AND table_schema = 'public') THEN 'EXISTS (legacy)' ELSE 'MISSING' END;
  `;

  const metalCheck = await executeQuery(metalCheckQuery);
  if (metalCheck) {
    output += '## Metal-Related Tables Status\n\n';
    output += '| Table Name | Status |\n';
    output += '|------------|--------|\n';
    metalCheck.forEach((table: any) => {
      const icon = table.status.includes('EXISTS') ? '✅' : '❌';
      output += `| ${table.table_name} | ${icon} ${table.status} |\n`;
    });
    output += '\n';
  }

  // Save to file
  const outputPath = path.join(process.cwd(), 'complete-database-structure.md');
  fs.writeFileSync(outputPath, output);
  
  console.log(`✅ Complete database structure analysis finished!`);
  console.log(`📄 Results saved to: ${outputPath}`);
  console.log(`📊 Found ${tables?.length || 0} tables`);
  console.log(`⚙️ Found ${functions?.length || 0} functions/procedures`);
  console.log(`🔒 Found ${policies?.length || 0} RLS policies`);
  console.log(`📇 Found ${indexes?.length || 0} custom indexes`);
}

// Run the analysis
getCompleteStructure().catch(console.error);
