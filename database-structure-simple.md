# Database Structure Analysis

Generated on: 2025-07-15T18:03:56.653Z

## Table Existence Check

| Table Name | Status | Error |
|------------|--------|-------|
| metal_type_mast | ✅ EXISTS | - |

### Columns in `metal_type_mast`:
- metal_type_id
- name
- description
- is_active
- created_at
- updated_at
- symbol

| karat_mast | ✅ EXISTS | - |

### Columns in `karat_mast`:
- karat_id
- description
- purity
- standard_wastage
- created_at
- updated_at
- density

| gold_colour_mast | ✅ EXISTS | - |

### Columns in `gold_colour_mast`:
- gold_colour_id
- description
- processing_complexity_factor
- created_at
- updated_at

| purity_mast | ✅ EXISTS | - |

### Columns in `purity_mast`:
- purity_id
- metal_type_id
- description
- purity_percentage
- standard_wastage_percentage
- density
- is_active
- created_at
- updated_at

| metal_colour_mast | ✅ EXISTS | - |

### Columns in `metal_colour_mast`:
- metal_colour_id
- metal_type_id
- description
- processing_complexity_factor
- is_active
- created_at
- updated_at

| metal_pool | ✅ EXISTS | - |
| orders | ✅ EXISTS | - |
| customer_mast | ✅ EXISTS | - |

### Columns in `customer_mast`:
- customer_id
- customer_code
- created_at
- updated_at
- customer_name
- description

| worker_mast | ✅ EXISTS | - |

### Columns in `worker_mast`:
- worker_id
- name
- is_active
- is_vendor
- shift_start
- shift_end
- created_at
- updated_at
- worker_type
- available_from
- available_to
- skills
- efficiency_factor

| process_mast | ✅ EXISTS | - |

### Columns in `process_mast`:
- process_id
- name
- description
- standard_time
- is_active
- requires_skill
- can_be_outsourced
- quality_checkpoints
- created_at
- updated_at
- complexity_hours_json
- default_wastage_pct
- default_recovery_pct
- dust_generated
- repeatable
- materials_issued
- materials_returned
- stones_required
- findings_required

| stone_type_mast | ✅ EXISTS | - |

### Columns in `stone_type_mast`:
- stone_type_id
- name
- description
- is_active
- created_at
- updated_at
- is_diamond

| stone_shape_mast | ✅ EXISTS | - |

### Columns in `stone_shape_mast`:
- shape_id
- name
- description
- is_active
- created_at
- updated_at

| stone_size_mast | ✅ EXISTS | - |

## Metal System Analysis

### ✅ `metal_type_mast` - Modern multi-metal types table
**Status**: EXISTS and ACCESSIBLE
**Record Count**: 5 (sample)
**Columns**: metal_type_id, name, description, is_active, created_at, updated_at, symbol
**Sample Data**:
```json
{
  "metal_type_id": "7a2214cc-cac7-42c1-875f-f0d131a84f90",
  "name": "Palladium",
  "description": "Palladium metal",
  "is_active": true,
  "created_at": "2025-04-03T17:04:40.639357",
  "updated_at": "2025-04-03T17:04:40.639357",
  "symbol": null
}
```

### ✅ `purity_mast` - Modern multi-metal purity table (expected)
**Status**: EXISTS and ACCESSIBLE
**Record Count**: 5 (sample)
**Columns**: purity_id, metal_type_id, description, purity_percentage, standard_wastage_percentage, density, is_active, created_at, updated_at
**Sample Data**:
```json
{
  "purity_id": "85fdcdeb-1726-4946-963c-10a678e9788d",
  "metal_type_id": "7f9d9278-2a09-48d8-b471-34b2109d9cc0",
  "description": "22KT",
  "purity_percentage": 91.67,
  "standard_wastage_percentage": 3.5,
  "density": 0,
  "is_active": true,
  "created_at": "2024-12-24T14:51:04.714975+00:00",
  "updated_at": "2024-12-24T14:51:04.714975+00:00"
}
```

### ✅ `metal_colour_mast` - Modern multi-metal color table (expected)
**Status**: EXISTS and ACCESSIBLE
**Record Count**: 5 (sample)
**Columns**: metal_colour_id, metal_type_id, description, processing_complexity_factor, is_active, created_at, updated_at
**Sample Data**:
```json
{
  "metal_colour_id": "25ec383e-fd95-46ff-85ab-a25e49e05032",
  "metal_type_id": "7f9d9278-2a09-48d8-b471-34b2109d9cc0",
  "description": "Yellow",
  "processing_complexity_factor": 1,
  "is_active": true,
  "created_at": "2024-12-24T14:51:04.714975+00:00",
  "updated_at": "2024-12-24T14:51:04.714975+00:00"
}
```

### ✅ `karat_mast` - Legacy gold-focused purity table
**Status**: EXISTS and ACCESSIBLE
**Record Count**: 4 (sample)
**Columns**: karat_id, description, purity, standard_wastage, created_at, updated_at, density
**Sample Data**:
```json
{
  "karat_id": "85fdcdeb-1726-4946-963c-10a678e9788d",
  "description": "22KT",
  "purity": 91.67,
  "standard_wastage": 3.5,
  "created_at": "2024-12-24T14:51:04.714975+00:00",
  "updated_at": "2024-12-24T14:51:04.714975+00:00",
  "density": 0
}
```

### ✅ `gold_colour_mast` - Legacy gold-focused color table
**Status**: EXISTS and ACCESSIBLE
**Record Count**: 3 (sample)
**Columns**: gold_colour_id, description, processing_complexity_factor, created_at, updated_at
**Sample Data**:
```json
{
  "gold_colour_id": "25ec383e-fd95-46ff-85ab-a25e49e05032",
  "description": "Yellow",
  "processing_complexity_factor": 1,
  "created_at": "2024-12-24T14:51:04.714975+00:00",
  "updated_at": "2024-12-24T14:51:04.714975+00:00"
}
```

