-- Debug Metal Types Data
-- Let's check what's actually in the table and why the API might not be returning it

-- 1. Check all records (including inactive)
SELECT 
    metal_type_id,
    name,
    symbol,
    description,
    is_active,
    created_at
FROM public.metal_type_mast 
ORDER BY created_at DESC;

-- 2. Check only active records (what the API should return)
SELECT 
    metal_type_id,
    name,
    symbol,
    description,
    is_active,
    created_at
FROM public.metal_type_mast 
WHERE is_active = true
ORDER BY name;

-- 3. Check data types and any potential issues
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'metal_type_mast' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 4. Check if there are any constraints or triggers
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'metal_type_mast' 
AND table_schema = 'public';

-- 5. Check RLS status
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename = 'metal_type_mast';

-- 6. Test the exact query the API is using
SELECT *
FROM public.metal_type_mast
WHERE is_active = true
ORDER BY name;
