#!/usr/bin/env node

/**
 * Database Structure Analysis Script
 * Connects to Supabase and extracts complete database structure
 * Saves results to database-structure-analysis.md for reference
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runQuery(sql, description) {
  console.log(`🔍 ${description}...`);
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      console.error(`❌ Error in ${description}:`, error);
      return null;
    }
    return data;
  } catch (err) {
    console.error(`❌ Exception in ${description}:`, err);
    return null;
  }
}

async function analyzeDatabase() {
  console.log('🚀 Starting database structure analysis...\n');

  let output = '# Database Structure Analysis\n\n';
  output += `Generated on: ${new Date().toISOString()}\n\n`;

  // 1. Get all tables summary
  const tablesSql = `
    SELECT 
        table_name,
        (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = 'public') as column_count
    FROM information_schema.tables t
    WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
    ORDER BY table_name;
  `;

  const tables = await runQuery(tablesSql, 'Getting all tables');
  if (tables) {
    output += '## All Tables Summary\n\n';
    output += '| Table Name | Column Count |\n';
    output += '|------------|-------------|\n';
    tables.forEach(table => {
      output += `| ${table.table_name} | ${table.column_count} |\n`;
    });
    output += '\n';
  }

  // 2. Check specific metal-related tables
  const metalTablesSql = `
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        AND (table_name LIKE '%metal%' 
             OR table_name LIKE '%karat%' 
             OR table_name LIKE '%gold%' 
             OR table_name LIKE '%purity%'
             OR table_name LIKE '%colour%'
             OR table_name LIKE '%color%')
    ORDER BY table_name;
  `;

  const metalTables = await runQuery(metalTablesSql, 'Getting metal-related tables');
  if (metalTables) {
    output += '## Metal-Related Tables\n\n';
    metalTables.forEach(table => {
      output += `- ${table.table_name}\n`;
    });
    output += '\n';
  }

  // 3. Check for specific tables mentioned in code
  const checkTablesSql = `
    SELECT 
        'metal_type_mast' as table_name,
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'metal_type_mast' AND table_schema = 'public') 
            THEN 'EXISTS'
            ELSE 'MISSING'
        END as status
    UNION ALL
    SELECT 
        'purity_mast',
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'purity_mast' AND table_schema = 'public') 
            THEN 'EXISTS'
            ELSE 'MISSING'
        END
    UNION ALL
    SELECT 
        'metal_colour_mast',
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'metal_colour_mast' AND table_schema = 'public') 
            THEN 'EXISTS'
            ELSE 'MISSING'
        END
    UNION ALL
    SELECT 
        'karat_mast',
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'karat_mast' AND table_schema = 'public') 
            THEN 'EXISTS (legacy)'
            ELSE 'MISSING'
        END
    UNION ALL
    SELECT 
        'gold_colour_mast',
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'gold_colour_mast' AND table_schema = 'public') 
            THEN 'EXISTS (legacy)'
            ELSE 'MISSING'
        END;
  `;

  const tableCheck = await runQuery(checkTablesSql, 'Checking specific tables');
  if (tableCheck) {
    output += '## Table Existence Check\n\n';
    output += '| Table Name | Status |\n';
    output += '|------------|--------|\n';
    tableCheck.forEach(table => {
      const icon = table.status.includes('EXISTS') ? '✅' : '❌';
      output += `| ${table.table_name} | ${icon} ${table.status} |\n`;
    });
    output += '\n';
  }

  // 4. Get detailed structure for key tables
  const keyTables = ['metal_type_mast', 'karat_mast', 'gold_colour_mast', 'metal_pool', 'orders'];
  
  for (const tableName of keyTables) {
    const structureSql = `
      SELECT 
          c.column_name,
          c.data_type,
          c.character_maximum_length,
          c.is_nullable,
          c.column_default,
          CASE 
              WHEN pk.column_name IS NOT NULL THEN 'PRIMARY KEY'
              WHEN fk.column_name IS NOT NULL THEN 'FOREIGN KEY -> ' || fk.foreign_table_name || '(' || fk.foreign_column_name || ')'
              ELSE ''
          END as constraints
      FROM information_schema.columns c
      LEFT JOIN (
          SELECT 
              kcu.column_name,
              kcu.table_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu 
              ON tc.constraint_name = kcu.constraint_name
          WHERE tc.constraint_type = 'PRIMARY KEY'
              AND tc.table_schema = 'public'
      ) pk ON c.table_name = pk.table_name AND c.column_name = pk.column_name
      LEFT JOIN (
          SELECT 
              kcu.column_name,
              kcu.table_name,
              ccu.table_name AS foreign_table_name,
              ccu.column_name AS foreign_column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu 
              ON tc.constraint_name = kcu.constraint_name
          JOIN information_schema.constraint_column_usage ccu 
              ON ccu.constraint_name = tc.constraint_name
          WHERE tc.constraint_type = 'FOREIGN KEY'
              AND tc.table_schema = 'public'
      ) fk ON c.table_name = fk.table_name AND c.column_name = fk.column_name
      WHERE c.table_name = '${tableName}'
          AND c.table_schema = 'public'
      ORDER BY c.ordinal_position;
    `;

    const structure = await runQuery(structureSql, `Getting structure for ${tableName}`);
    if (structure && structure.length > 0) {
      output += `## Table: ${tableName}\n\n`;
      output += '| Column | Type | Length | Nullable | Default | Constraints |\n';
      output += '|--------|------|--------|----------|---------|-------------|\n';
      structure.forEach(col => {
        output += `| ${col.column_name} | ${col.data_type} | ${col.character_maximum_length || '-'} | ${col.is_nullable} | ${col.column_default || '-'} | ${col.constraints || '-'} |\n`;
      });
      output += '\n';
    }
  }

  // Save to file
  const outputPath = path.join(process.cwd(), 'database-structure-analysis.md');
  fs.writeFileSync(outputPath, output);
  
  console.log(`✅ Database structure analysis complete!`);
  console.log(`📄 Results saved to: ${outputPath}`);
}

// Run the analysis
analyzeDatabase().catch(console.error);
